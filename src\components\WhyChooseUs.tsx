import { Target, Star, Users } from "lucide-react";

const WhyChooseUs = () => {
  const features = [
    {
      icon: <Target className="w-12 h-12" />,
      title: "Personalized Care",
      description: "Every treatment is tailored for you"
    },
    {
      icon: <Star className="w-12 h-12" />,
      title: "Premium Products",
      description: "We only use top-tier, safe beauty brands"
    },
    {
      icon: <Users className="w-12 h-12" />,
      title: "Expert Stylists",
      description: "Experienced professionals in modern trends"
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-salon-champagne/20 to-salon-rose-gold/10">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
            Why Choose Prenses Kuaför?
          </h2>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-12">
          {features.map((feature, index) => (
            <div 
              key={index}
              className="text-center group"
            >
              <div className="inline-flex p-6 rounded-full bg-gradient-to-br from-salon-rose-gold/20 to-salon-champagne/30 text-salon-burgundy mb-8 group-hover:scale-110 transition-transform duration-300 shadow-soft">
                {feature.icon}
              </div>
              
              <h3 className="text-2xl font-serif font-semibold text-primary mb-6">
                {feature.title}
              </h3>
              
              <p className="text-muted-foreground leading-relaxed text-lg">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
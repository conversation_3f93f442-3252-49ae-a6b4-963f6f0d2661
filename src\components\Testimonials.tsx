import { Star, Quote } from "lucide-react";

const Testimonials = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      rating: 5,
      text: "Prenses Kuaför'de aldığım hizmetlerin kalitesi gerçekten mükemmel. Saçlarım hiç bu kadar güzel görünmemişti. Kesinlikle tavsiye ederim!",
      service: "Saç Tasarımı & Balayage"
    },
    {
      name: "Ayşe Demir",
      rating: 5,
      text: "Kalıcı makyaj için gelmiştim ve sonuçtan çok memnunum. Ekip çok profesyonel ve samimi. Kendimi gerçekten prenses gibi hissettim.",
      service: "Kalıcı Makyaj"
    },
    {
      name: "<PERSON>eh<PERSON>",
      rating: 5,
      text: "Salonun atmosferi ve ekibin yaklaşımı harika. Mikro kaynak için gelmiştim, doğal görünümü ile çok beğendim. Herkese öneriyorum!",
      service: "<PERSON><PERSON><PERSON>"
    }
  ];

  return (
    <section className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-4">
            <Quote className="w-8 h-8 text-salon-gold" />
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
            What Our Clients Say
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
            Müşterilerimizin deneyimleri bizim en büyük motivasyonumuz
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={index}
              className="bg-gradient-to-br from-card to-salon-champagne/20 rounded-3xl p-8 shadow-soft hover:shadow-elegant transition-all duration-300 hover:scale-105"
            >
              {/* Rating */}
              <div className="flex items-center mb-6">
                {Array.from({ length: testimonial.rating }).map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-salon-gold fill-current" />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="text-muted-foreground leading-relaxed mb-6 italic">
                "{testimonial.text}"
              </blockquote>

              {/* Client Info */}
              <div className="border-t border-border/50 pt-6">
                <h4 className="font-semibold text-primary mb-1">
                  {testimonial.name}
                </h4>
                <p className="text-sm text-salon-burgundy">
                  {testimonial.service}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  Scissors,
  Palette,
  Sparkles,
  Eye,
  Hand,
  Brush,
  Heart,
  Zap,
  Crown,
  Phone,
  Calendar,
  ArrowUp
} from "lucide-react";

const Services = () => {
  const services = [
    {
      icon: <Scissors className="w-12 h-12" />,
      title: "Hair Design",
      description: "Trendy cuts and styles tailored to your unique look.",
      color: "salon-burgundy"
    },
    {
      icon: <Sparkles className="w-12 h-12" />,
      title: "Micro Extensions",
      description: "Add volume and length naturally with our premium extensions.",
      color: "salon-rose-gold"
    },
    {
      icon: <Palette className="w-12 h-12" />,
      title: "Ombre & Balayage",
      description: "Smooth, modern color transitions for a radiant finish.",
      color: "salon-gold"
    },
    {
      icon: <Eye className="w-12 h-12" />,
      title: "Permanent Makeup",
      description: "Flawless definition that lasts — brows, lips, and more.",
      color: "salon-burgundy"
    },
    {
      icon: <Heart className="w-12 h-12" />,
      title: "Medical Treatments",
      description: "Professional solutions for scalp and hair health.",
      color: "salon-gold"
    },
    {
      icon: <Hand className="w-12 h-12" />,
      title: "Manicure / Pedicure",
      description: "Luxurious care for hands and feet with beautiful results.",
      color: "salon-rose-gold"
    },
    {
      icon: <Brush className="w-12 h-12" />,
      title: "Porcelain Makeup",
      description: "HD-ready finish for special events and photoshoots.",
      color: "salon-burgundy"
    },
    {
      icon: <Zap className="w-12 h-12" />,
      title: "Hair Removal",
      description: "Safe, gentle waxing and threading for silky smooth skin.",
      color: "salon-gold"
    },
    {
      icon: <Crown className="w-12 h-12" />,
      title: "Eyebrows",
      description: "Perfectly shaped, tinted, and styled to enhance your beauty.",
      color: "salon-rose-gold"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-salon-champagne/20 to-background">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <Sparkles className="w-8 h-8 text-salon-gold animate-pulse" />
            </div>
            
            <h1 className="text-4xl lg:text-6xl font-serif font-bold text-primary mb-6">
              Our Signature Services
            </h1>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Explore the full range of beauty and hair treatments we offer.
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {services.map((service, index) => (
              <div 
                key={index}
                className="group bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl p-8 shadow-soft hover:shadow-elegant transition-all duration-300 hover:scale-105 cursor-pointer border border-border/50"
              >
                <div className={`inline-flex p-4 rounded-2xl bg-${service.color}/10 text-${service.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {service.icon}
                </div>
                
                <h3 className="text-2xl font-serif font-semibold text-primary mb-4 group-hover:text-salon-burgundy transition-colors">
                  {service.title}
                </h3>
                
                <p className="text-muted-foreground leading-relaxed">
                  {service.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Consultation Section */}
      <section className="py-20 bg-gradient-to-br from-salon-rose-gold/20 to-salon-champagne/10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-8">
              Not Sure What You Need?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
              Our stylists are here to help. Let us guide you toward the perfect treatment.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Button variant="salon" size="xl" className="shadow-elegant group">
                <Phone className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Book a Free Consultation
              </Button>
              
              <Button variant="salonSecondary" size="xl" className="group">
                <Calendar className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                View Price List
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Scroll to Top Button */}
      <div className="fixed bottom-24 right-6 z-40">
        <Button
          variant="secondary"
          size="icon"
          className="w-12 h-12 rounded-full shadow-elegant hover:scale-110 transition-transform"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <ArrowUp className="w-6 h-6" />
        </Button>
      </div>

      <Footer />
    </div>
  );
};

export default Services;
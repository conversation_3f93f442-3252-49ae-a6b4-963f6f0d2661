import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, Sparkles } from "lucide-react";
import heroImage from "@/assets/hero-salon.jpg";

const Hero = () => {
  return (
    <section id="home" className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img 
          src={heroImage} 
          alt="Elegant hair salon interior" 
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-salon-rose-gold/70 via-salon-champagne/60 to-salon-burgundy/85"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 text-center px-4 max-w-6xl mx-auto">
        <div className="animate-fade-in">
          <div className="flex justify-center mb-6">
            <Sparkles className="w-8 h-8 text-salon-gold animate-pulse" />
          </div>
          
          <h1 className="hero-title mb-6">
            Prenses <PERSON>
          </h1>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif font-medium text-primary mb-8">
            Where Confidence Begins
          </h2>
          
          <p className="hero-subtitle mb-12">
            Discover professional beauty services in a luxurious, welcoming space.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16">
            <Button variant="salon" size="xl" className="shadow-elegant">
              Book Appointment
            </Button>
            <Button variant="salonSecondary" size="xl">
              View Our Services
            </Button>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
        <div className="animate-bounce">
          <ChevronDown className="w-8 h-8 text-primary/80" />
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 rounded-full bg-salon-gold/20 animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-16 h-16 rounded-full bg-salon-rose-gold/30 animate-pulse delay-1000"></div>
    </section>
  );
};

export default Hero;
import { But<PERSON> } from "@/components/ui/button";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { 
  Crown, 
  Heart, 
  Star, 
  Users, 
  Award, 
  Sparkles,
  Calendar,
  Phone
} from "lucide-react";
import salonTeam from "@/assets/salon-team.jpg";
import salonInterior from "@/assets/salon-interior.jpg";

const AboutUs = () => {
  const features = [
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Client-First Philosophy",
      description: "Every client is unique. That's why we tailor each experience to your needs, style, and vision.",
      color: "salon-burgundy"
    },
    {
      icon: <Users className="w-8 h-8" />,
      title: "Expert Stylists & Artists",
      description: "Our talented team is trained in the latest techniques, trends, and treatments — blending artistry with skill.",
      color: "salon-gold"
    },
    {
      icon: <Award className="w-8 h-8" />,
      title: "Premium Products",
      description: "We use only the safest, highest-quality hair and skin care products to ensure lasting beauty and protection.",
      color: "salon-rose-gold"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "Comfort & Elegance",
      description: "From the moment you walk in, our salon offers a relaxing, welcoming atmosphere where you can truly unwind.",
      color: "salon-burgundy"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-salon-champagne/20 to-background">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <Crown className="w-8 h-8 text-salon-gold animate-pulse" />
            </div>
            
            <h1 className="text-4xl lg:text-6xl font-serif font-bold text-primary mb-6">
              Meet the Artists Behind the Beauty
            </h1>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Discover the passion, expertise, and dedication that makes Prenses Kuaför your trusted beauty destination.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Content */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Heart className="w-6 h-6 text-salon-burgundy" />
                  <span className="text-salon-burgundy font-medium">Our Story</span>
                </div>
                
                <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary leading-tight">
                  Beauty is More Than Appearance
                </h2>
                
                <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                  <p>
                    At Prenses Kuaför, beauty is more than appearance — it's a feeling. Founded with a passion for transformation and self-confidence, our salon has become a trusted destination for women seeking exceptional care, personalized attention, and a touch of elegance.
                  </p>
                  
                  <p>
                    With over a decade of experience in the beauty industry, we bring a blend of creativity, professionalism, and warmth to every service. Whether it's a bold new haircut, flawless makeup, or a subtle color refresh, our goal is simple: help you feel radiant, confident, and completely yourself.
                  </p>
                </div>
              </div>
            </div>

            {/* Image */}
            <div className="relative">
              <div className="relative rounded-3xl overflow-hidden shadow-elegant">
                <img 
                  src={salonTeam} 
                  alt="Our professional team at Prenses Kuaför" 
                  className="w-full h-[600px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-salon-burgundy/20 to-transparent"></div>
              </div>
              
              {/* Decorative Elements */}
              <div className="absolute -top-6 -right-6 w-24 h-24 rounded-full bg-salon-gold/20 animate-pulse"></div>
              <div className="absolute -bottom-6 -left-6 w-20 h-20 rounded-full bg-salon-rose-gold/30 animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </section>

      {/* What Makes Us Different Section */}
      <section className="py-20 bg-gradient-to-br from-salon-champagne/10 to-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="flex justify-center mb-6">
              <Star className="w-8 h-8 text-salon-gold animate-pulse" />
            </div>
            
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
              What Makes Us Different
            </h2>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Discover the unique qualities that set Prenses Kuaför apart from other salons.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index}
                className="group bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl p-8 shadow-soft hover:shadow-elegant transition-all duration-300 hover:scale-105 cursor-pointer border border-border/50 text-center"
              >
                <div className={`inline-flex p-4 rounded-2xl bg-${feature.color}/10 text-${feature.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {feature.icon}
                </div>
                
                <h3 className="text-xl font-serif font-semibold text-primary mb-4 group-hover:text-salon-burgundy transition-colors">
                  {feature.title}
                </h3>
                
                <p className="text-muted-foreground leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Image */}
            <div className="relative order-2 lg:order-1">
              <div className="relative rounded-3xl overflow-hidden shadow-elegant">
                <img 
                  src={salonInterior} 
                  alt="Elegant salon interior at Prenses Kuaför" 
                  className="w-full h-[500px] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-salon-burgundy/20 to-transparent"></div>
              </div>
              
              {/* Decorative Elements */}
              <div className="absolute -top-6 -left-6 w-24 h-24 rounded-full bg-salon-rose-gold/20 animate-pulse"></div>
              <div className="absolute -bottom-6 -right-6 w-20 h-20 rounded-full bg-salon-gold/30 animate-pulse delay-1000"></div>
            </div>

            {/* Content */}
            <div className="space-y-8 order-1 lg:order-2">
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Crown className="w-6 h-6 text-salon-gold" />
                  <span className="text-salon-gold font-medium">Our Mission</span>
                </div>
                
                <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary leading-tight">
                  Empowering Women Through Beauty
                </h2>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  We believe that when you look your best, you feel your best — and that confidence is truly transformative. Our mission is to empower every woman who walks through our doors to embrace her unique beauty and leave feeling radiant, confident, and ready to conquer the world.
                </p>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  Whether you're preparing for a special occasion or simply need a moment of self-care, Prenses Kuaför is here to welcome you with open arms and expert hands.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-br from-salon-rose-gold/20 to-salon-champagne/10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-8">
              Ready to Experience the Difference?
            </h2>
            
            <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
              Whether you're preparing for a special occasion or simply need a moment of self-care, Prenses Kuaför is here to welcome you.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Button variant="salon" size="xl" className="shadow-elegant group">
                <Calendar className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Book Your Appointment
              </Button>
              
              <Button variant="salonSecondary" size="xl" className="group">
                <Sparkles className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Explore Our Services
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Floating CTA */}
      <div className="fixed bottom-6 right-6 z-40">
        <Button 
          variant="salon" 
          size="lg" 
          className="shadow-elegant animate-pulse rounded-full px-8 py-4"
        >
          <Phone className="w-5 h-5 mr-2" />
          Call Now
        </Button>
      </div>

      <Footer />
    </div>
  );
};

export default AboutUs;

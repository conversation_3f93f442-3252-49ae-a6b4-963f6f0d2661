import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  Scissors,
  Palette,
  Sparkles,
  Eye,
  Hand,
  Brush,
  Heart,
  Zap,
  Crown
} from "lucide-react";

const Services = () => {
  const navigate = useNavigate();

  const services = [
    {
      icon: <Scissors className="w-8 h-8" />,
      title: "Saç Tasarımı",
      description: "Kişiliğinizi yansıtan özel kesim ve şekillendirme",
      color: "salon-burgundy"
    },
    {
      icon: <Palette className="w-8 h-8" />,
      title: "Ombre & Balayage",
      description: "<PERSON><PERSON>al görünümlü renk geçişleri ve modern teknikler",
      color: "salon-gold"
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "<PERSON><PERSON><PERSON>",
      description: "Kalıcı uzunluk ve hacim artırma çözümleri",
      color: "salon-rose-gold"
    },
    {
      icon: <Eye className="w-8 h-8" />,
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      description: "<PERSON><PERSON>, göz ve dudak için uzun süreli güzellik",
      color: "salon-burgundy"
    },
    {
      icon: <Heart className="w-8 h-8" />,
      title: "Medikal Bakım",
      description: "Saç dökülmesi ve kafa derisi sorunları tedavisi",
      color: "salon-gold"
    },
    {
      icon: <Hand className="w-8 h-8" />,
      title: "Manikür / Pedikür",
      description: "El ve ayak bakımı ile lüks spa deneyimi",
      color: "salon-rose-gold"
    },
    {
      icon: <Brush className="w-8 h-8" />,
      title: "Porselen Makyaj",
      description: "HD kalitede pürüzsüz cilt görünümü",
      color: "salon-burgundy"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Epilasyon",
      description: "Güvenli ve etkili tüy alma yöntemleri",
      color: "salon-gold"
    },
    {
      icon: <Crown className="w-8 h-8" />,
      title: "Kaş Tasarımı",
      description: "Şekillendirmeden boyamaya kaş güzelliği",
      color: "salon-rose-gold"
    }
  ];

  return (
    <section id="services" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-4">
            <Sparkles className="w-8 h-8 text-salon-gold animate-pulse" />
          </div>
          
          <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
            Explore Our Full Range of Beauty Services
          </h2>
          
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Her güzellik ihtiyacınız için profesyonel hizmetler. 
            Deneyimli ekibimizle mükemmel sonuçlar için randevunuzu alın.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {services.slice(0, 6).map((service, index) => (
            <div 
              key={index}
              className="service-card group cursor-pointer"
            >
              <div className={`inline-flex p-4 rounded-2xl bg-${service.color}/10 text-${service.color} mb-6 group-hover:scale-110 transition-transform duration-300`}>
                {service.icon}
              </div>
              
              <h3 className="text-xl font-serif font-semibold text-primary mb-4">
                {service.title}
              </h3>
              
              <p className="text-muted-foreground leading-relaxed">
                {service.description}
              </p>
            </div>
          ))}
        </div>

        {/* CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-salon-rose-gold/20 to-salon-champagne/20 rounded-3xl p-12">
            <h3 className="text-3xl font-serif font-bold text-primary mb-6">
              Want to see all our services?
            </h3>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button variant="salon" size="xl">
                Book Appointment
              </Button>
              <Button
                variant="salonSecondary"
                size="xl"
                onClick={() => navigate('/services')}
              >
                See All Services
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Services;
import { MapPin, Phone, Clock, Instagram, MessageCircle } from "lucide-react";
import { Button } from "@/components/ui/button";

const Footer = () => {
  return (
    <footer id="contact" className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 gap-12">
          {/* Brand & Description */}
          <div className="lg:col-span-2 space-y-6">
            <h3 className="text-3xl font-serif font-bold">Prenses Kuaför</h3>
            <p className="text-primary-foreground/80 leading-relaxed max-w-md">
              10 yıldır kadınları güzel hissettirmenin gururunu yaşıyoruz. 
              Her müşterimiz bizim için bir prenses, her hizmetimiz bir sanat eseri.
            </p>
            
            {/* Social Media */}
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" className="text-primary-foreground hover:bg-primary-foreground/10">
                <Instagram className="w-5 h-5" />
              </Button>
              <Button variant="ghost" size="icon" className="text-primary-foreground hover:bg-primary-foreground/10">
                <MessageCircle className="w-5 h-5" />
              </Button>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-6">
            <h4 className="text-xl font-serif font-semibold">İletişim</h4>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-salon-gold mt-1 flex-shrink-0" />
                <div>
                  <p className="text-primary-foreground/90">
                    Atatürk Caddesi No: 123<br />
                    Merkez/İstanbul
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Phone className="w-5 h-5 text-salon-gold flex-shrink-0" />
                <p className="text-primary-foreground/90">+90 532 123 45 67</p>
              </div>
              
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-salon-gold mt-1 flex-shrink-0" />
                <div className="text-primary-foreground/90">
                  <p>Pazartesi - Cumartesi</p>
                  <p>09:00 - 19:00</p>
                  <p className="text-sm text-primary-foreground/60 mt-1">Pazar kapalı</p>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-6">
            <h4 className="text-xl font-serif font-semibold">Hızlı Bağlantılar</h4>
            
            <nav className="flex flex-col space-y-3">
              <a href="#home" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Ana Sayfa
              </a>
              <a href="#services" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Hizmetler
              </a>
              <a href="#about" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                Hakkımızda
              </a>
              <a href="#contact" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">
                İletişim
              </a>
            </nav>
            
            {/* WhatsApp CTA */}
            <Button variant="secondary" size="lg" className="w-full mt-6">
              <MessageCircle className="w-4 h-4 mr-2" />
              WhatsApp ile İletişim
            </Button>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-primary-foreground/20 mt-12 pt-8 text-center">
          <p className="text-primary-foreground/60">
            © 2024 Prenses Kuaför. Tüm hakları saklıdır.
          </p>
        </div>
      </div>

      {/* Floating WhatsApp Button */}
      <div className="fixed bottom-6 right-6 z-50">
        <Button 
          variant="secondary" 
          size="icon" 
          className="w-16 h-16 rounded-full shadow-glow hover:scale-110 transition-transform animate-pulse"
        >
          <MessageCircle className="w-8 h-8" />
        </Button>
      </div>
    </footer>
  );
};

export default Footer;
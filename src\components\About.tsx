import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Heart, Star } from "lucide-react";
import salonInterior from "@/assets/salon-interior.jpg";

const About = () => {
  return (
    <section id="about" className="py-20 bg-gradient-to-br from-salon-champagne/30 to-background">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="flex items-center space-x-2 mb-4">
                <Crown className="w-6 h-6 text-salon-gold" />
                <span className="text-salon-burgundy font-medium">Hakkımızda</span>
              </div>
              
              <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary leading-tight">
                More Than a Salon — It's an Experience
              </h2>
              
              <p className="text-lg text-muted-foreground leading-relaxed">
                With over 10 years of excellence in hairstyling and beauty, Prenses Kuaför is a destination for women seeking personalized care and elegance.
              </p>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <Heart className="w-6 h-6 text-salon-burgundy mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-primary mb-2">Kişisel Yaklaşım</h3>
                  <p className="text-muted-foreground">Her müşteriye özel güzellik çözümleri</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Star className="w-6 h-6 text-salon-gold mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-primary mb-2">Kaliteli Ürünler</h3>
                  <p className="text-muted-foreground">Sadece premium ve güvenli ürünler</p>
                </div>
              </div>
            </div>

            <Button variant="salon" size="lg" className="mt-8">
              Learn More
            </Button>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="relative rounded-3xl overflow-hidden shadow-elegant">
              <img 
                src={salonInterior} 
                alt="Elegant salon interior" 
                className="w-full h-[600px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-salon-burgundy/20 to-transparent"></div>
            </div>
            
            {/* Decorative Elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 rounded-full bg-salon-gold/20 animate-pulse"></div>
            <div className="absolute -bottom-6 -left-6 w-20 h-20 rounded-full bg-salon-rose-gold/30 animate-pulse delay-1000"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Prenses Kuaför Design System - Elegant feminine beauty salon aesthetic */

@layer base {
  :root {
    /* Base colors - soft, elegant palette */
    --background: 0 0% 100%;
    --foreground: 345 25% 15%;

    --card: 350 40% 98%;
    --card-foreground: 345 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 345 25% 15%;

    /* Primary - Deep burgundy for elegance */
    --primary: 345 65% 25%;
    --primary-foreground: 350 40% 98%;

    /* Secondary - Rose gold tones */
    --secondary: 350 35% 85%;
    --secondary-foreground: 345 25% 15%;

    /* Muted - Soft champagne */
    --muted: 48 30% 95%;
    --muted-foreground: 345 15% 45%;

    /* Accent - Warm rose */
    --accent: 350 45% 75%;
    --accent-foreground: 345 25% 15%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 350 25% 90%;
    --input: 350 25% 92%;
    --ring: 345 65% 25%;

    --radius: 1rem;

    /* Custom salon colors */
    --salon-rose-gold: 350 45% 85%;
    --salon-champagne: 48 35% 92%;
    --salon-burgundy: 345 65% 25%;
    --salon-nude: 30 20% 90%;
    --salon-gold: 45 55% 70%;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(350 45% 85%) 0%, hsl(48 35% 92%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(350 40% 98%) 0%, hsl(350 45% 95%) 100%);
    --gradient-button: linear-gradient(135deg, hsl(345 65% 25%) 0%, hsl(345 55% 35%) 100%);
    
    /* Shadows */
    --shadow-soft: 0 4px 20px hsl(350 25% 85% / 0.3);
    --shadow-elegant: 0 8px 30px hsl(345 65% 25% / 0.15);
    --shadow-glow: 0 0 40px hsl(350 45% 75% / 0.3);
    
    /* Animations */
    --transition-smooth: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-family: 'Poppins', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Playfair Display', serif;
  }
}

@layer components {
  /* Button variants for salon aesthetic */
  .btn-salon-primary {
    @apply bg-gradient-to-r from-salon-burgundy to-salon-burgundy/90 text-primary-foreground 
           rounded-full px-8 py-4 font-medium transition-all duration-300 
           hover:scale-105 hover:shadow-elegant;
  }
  
  .btn-salon-secondary {
    @apply bg-gradient-to-r from-salon-rose-gold to-salon-champagne text-primary 
           rounded-full px-8 py-4 font-medium transition-all duration-300 
           hover:scale-105 hover:shadow-soft border border-accent/20;
  }
  
  /* Hero text effects */
  .hero-title {
    @apply text-6xl md:text-7xl lg:text-8xl font-bold text-primary 
           drop-shadow-lg leading-tight;
  }
  
  .hero-subtitle {
    @apply text-xl md:text-2xl text-white font-light
           max-w-2xl mx-auto leading-relaxed;
  }
  
  /* Card animations */
  .service-card {
    @apply bg-gradient-to-br from-card to-salon-champagne/30 
           rounded-3xl p-8 transition-all duration-500 
           hover:scale-105 hover:shadow-elegant hover:bg-gradient-to-br hover:from-salon-rose-gold/20 hover:to-salon-champagne/50;
  }
  
  /* Smooth scroll behavior */
  .smooth-scroll {
    scroll-behavior: smooth;
  }
}
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Sparkles, Calendar, CreditCard } from "lucide-react";

const CallToAction = () => {
  return (
    <section className="py-20 bg-gradient-to-br from-salon-rose-gold/30 via-salon-champagne/20 to-salon-burgundy/10">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto text-center">
          {/* Decorative Element */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-2">
              <Sparkles className="w-8 h-8 text-salon-gold animate-pulse" />
              <Sparkles className="w-6 h-6 text-salon-burgundy animate-pulse delay-300" />
              <Sparkles className="w-8 h-8 text-salon-rose-gold animate-pulse delay-700" />
            </div>
          </div>

          {/* Main Content */}
          <h2 className="text-4xl lg:text-6xl font-serif font-bold text-primary mb-8 leading-tight">
            Ready for a New You?
          </h2>
          
          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
            Join hundreds of women who trust Prenses Kuaför for their beauty journey.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-12">
            <Button variant="salon" size="xl" className="shadow-elegant group">
              <Calendar className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
              Book Your Appointment Now
            </Button>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-12 text-muted-foreground">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-salon-gold"></div>
              <span>10+ Yıl Deneyim</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-salon-burgundy"></div>
              <span>1000+ Mutlu Müşteri</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 rounded-full bg-salon-rose-gold"></div>
              <span>Premium Ürünler</span>
            </div>
          </div>
        </div>

        {/* Background Decorations */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-20 left-10 w-32 h-32 rounded-full bg-salon-gold/10 animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-24 h-24 rounded-full bg-salon-burgundy/10 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-20 w-16 h-16 rounded-full bg-salon-rose-gold/20 animate-pulse delay-500"></div>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;
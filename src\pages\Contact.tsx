import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import {
  MapPin,
  Phone,
  Mail,
  Clock,
  Send,
  Instagram,
  Facebook,
  MessageCircle,
  Calendar,
  HelpCircle,
  ArrowUp
} from "lucide-react";

const Contact = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="pt-32 pb-16 bg-gradient-to-br from-salon-champagne/20 to-background">
        <div className="container mx-auto px-4 text-center">
          <div className="animate-fade-in">
            <div className="flex justify-center mb-6">
              <MessageCircle className="w-8 h-8 text-salon-gold animate-pulse" />
            </div>
            
            <h1 className="text-4xl lg:text-6xl font-serif font-bold text-primary mb-6">
              Let's Talk Beauty
            </h1>
            
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Have a question? Ready to book your next appointment? We'd love to hear from you.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form & Info Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16">
            
            {/* Contact Form */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <Send className="w-6 h-6 text-salon-burgundy" />
                  <span className="text-salon-burgundy font-medium">Send us a Message</span>
                </div>
                
                <h2 className="text-3xl lg:text-4xl font-serif font-bold text-primary leading-tight">
                  Contact Form
                </h2>
              </div>

              <form className="space-y-6">
                <div className="space-y-2">
                  <label htmlFor="fullName" className="text-sm font-medium text-foreground">
                    Full Name *
                  </label>
                  <Input
                    id="fullName"
                    type="text"
                    placeholder="Enter your full name"
                    className="h-12 rounded-xl border-border/50 focus:border-salon-burgundy"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-foreground">
                    Email Address *
                  </label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="Enter your email address"
                    className="h-12 rounded-xl border-border/50 focus:border-salon-burgundy"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="phone" className="text-sm font-medium text-foreground">
                    Phone Number *
                  </label>
                  <Input
                    id="phone"
                    type="tel"
                    placeholder="Enter your phone number"
                    className="h-12 rounded-xl border-border/50 focus:border-salon-burgundy"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="message" className="text-sm font-medium text-foreground">
                    Message *
                  </label>
                  <Textarea
                    id="message"
                    placeholder="Tell us how we can help you..."
                    className="min-h-32 rounded-xl border-border/50 focus:border-salon-burgundy resize-none"
                    required
                  />
                </div>

                <Button variant="salon" size="xl" className="w-full shadow-elegant group">
                  <Send className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                  Send Message
                </Button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8">
              <div className="space-y-4">
                <div className="flex items-center space-x-2 mb-4">
                  <MapPin className="w-6 h-6 text-salon-gold" />
                  <span className="text-salon-gold font-medium">Contact Information</span>
                </div>
                
                <h2 className="text-3xl lg:text-4xl font-serif font-bold text-primary leading-tight">
                  Visit Our Salon
                </h2>
              </div>

              <div className="space-y-8">
                {/* Address */}
                <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50">
                  <div className="p-3 rounded-2xl bg-salon-burgundy/10">
                    <MapPin className="w-6 h-6 text-salon-burgundy" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-2">Salon Address</h3>
                    <p className="text-muted-foreground leading-relaxed">
                      Prenses Kuaför<br />
                      Güllük caddesi, 57<br />
                      Antalya, Turkey 07050
                    </p>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50">
                  <div className="p-3 rounded-2xl bg-salon-gold/10">
                    <Phone className="w-6 h-6 text-salon-gold" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-2">Phone</h3>
                    <p className="text-muted-foreground">+90 541 403 80 47</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50">
                  <div className="p-3 rounded-2xl bg-salon-rose-gold/10">
                    <Mail className="w-6 h-6 text-salon-rose-gold" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-2">Email</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>

                {/* Opening Hours */}
                <div className="flex items-start space-x-4 p-6 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50">
                  <div className="p-3 rounded-2xl bg-salon-burgundy/10">
                    <Clock className="w-6 h-6 text-salon-burgundy" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-2">Opening Hours</h3>
                    <div className="text-muted-foreground space-y-1">
                      <p>Monday – Saturday: 9:00 AM – 8:30 PM</p>
                      <p>Sunday: Closed</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Google Map Section */}
      <section className="py-20 bg-gradient-to-br from-salon-champagne/10 to-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
              Find Us on the Map
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Located in the heart of Antalya, we're easy to find and convenient to reach.
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="relative rounded-3xl overflow-hidden shadow-elegant h-96">
              {/* Google Map Embed */}
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3186.2!2d30.7133!3d36.8969!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMzbCsDUzJzQ4LjgiTiAzMMKwNDInNDcuOSJF!5e0!3m2!1sen!2str!4v1234567890!5m2!1sen!2str&q=G%C3%BCll%C3%BCk+caddesi+57+Antalya+Turkey"
                width="100%"
                height="100%"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
                title="Prenses Kuaför Location"
              ></iframe>
            </div>
          </div>
        </div>
      </section>

      {/* Get Social Section */}
      <section className="py-20 bg-background">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-6">
              Get Social
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto leading-relaxed">
              Follow us and stay inspired with our latest work and beauty tips.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {/* WhatsApp */}
            <div className="text-center p-8 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50 hover:shadow-elegant transition-all duration-300">
              <div className="inline-flex p-4 rounded-2xl bg-green-500/10 text-green-600 mb-6">
                <MessageCircle className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-serif font-semibold text-primary mb-4">WhatsApp Chat</h3>
              <p className="text-muted-foreground mb-6">Quick questions? Chat with us directly!</p>
              <Button
                variant="outline"
                size="lg"
                className="w-full"
                onClick={() => window.open('https://wa.me/905414038047', '_blank')}
              >
                Start Chat
              </Button>
            </div>

            {/* Instagram */}
            <div className="text-center p-8 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50 hover:shadow-elegant transition-all duration-300">
              <div className="inline-flex p-4 rounded-2xl bg-pink-500/10 text-pink-600 mb-6">
                <Instagram className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-serif font-semibold text-primary mb-4">Instagram</h3>
              <p className="text-muted-foreground mb-6">@prenseskuaforguzellik</p>
              <Button variant="outline" size="lg" className="w-full">
                Follow Us
              </Button>
            </div>

            {/* Facebook */}
            <div className="text-center p-8 bg-gradient-to-br from-card to-salon-champagne/10 rounded-3xl border border-border/50 hover:shadow-elegant transition-all duration-300">
              <div className="inline-flex p-4 rounded-2xl bg-blue-500/10 text-blue-600 mb-6">
                <Facebook className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-serif font-semibold text-primary mb-4">Facebook</h3>
              <p className="text-muted-foreground mb-6">facebook.com/prenseskuaforguzellik</p>
              <Button variant="outline" size="lg" className="w-full">
                Like Page
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-20 bg-gradient-to-br from-salon-rose-gold/20 to-salon-champagne/10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl lg:text-5xl font-serif font-bold text-primary mb-8">
              We're Here to Help You Shine
            </h2>

            <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto leading-relaxed">
              Whether it's a quick touch-up or a full transformation — reach out and let's create beauty together.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <Button variant="salon" size="xl" className="shadow-elegant group">
                <Calendar className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Book an Appointment
              </Button>

              <Button variant="salonSecondary" size="xl" className="group">
                <HelpCircle className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                Ask Us a Question
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Scroll to Top Button */}
      <div className="fixed bottom-24 right-6 z-40">
        <Button
          variant="secondary"
          size="icon"
          className="w-12 h-12 rounded-full shadow-elegant hover:scale-110 transition-transform"
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <ArrowUp className="w-6 h-6" />
        </Button>
      </div>

      <Footer />
    </div>
  );
};

export default Contact;
